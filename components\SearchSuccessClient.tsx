'use client';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ResultVisualization } from '@/components/ui/result-visualization';
import { ArrowLeft, Download, Share2 } from 'lucide-react';

type Report = {
  probability: number;
  summary: string;
  details: string;
  analysis?: string;
};

type InputData = {
  itching_intensity: string;
  itching_worse_at_night: boolean;
  rash_present: boolean;
  rash_locations: string[];
  visible_burrows: boolean;
  onset_days: number;
  close_contact_scabies: boolean;
  lives_in_crowded_setting: boolean;
  attempted_treatment: string;
  skin_scraping_positive: boolean;
  immune_status: string;
  thick_crusts_on_skin: boolean;
};

type SessionData = {
  input: InputData;
  probability: number;
  summary: string;
  details: string;
  timestamp: string;
};

export default function SearchSuccessClient() {
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        // Get the stored session data
        const storedData = localStorage.getItem("scabbiaCheckupData");
        if (!storedData) {
          throw new Error("Nessun dato trovato. Per favore, completa il questionario.");
        }

        const sessionData: SessionData = JSON.parse(storedData);
        
        // Call the analyze-answers API
        const response = await fetch("/api/analyze-answers", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ input: sessionData.input }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Errore durante l'analisi delle risposte");
        }

        const { answer } = await response.json();

        // Create the report with the AI analysis
        const report: Report = {
          probability: sessionData.probability,
          summary: sessionData.summary,
          details: sessionData.details,
          analysis: answer,
        };

        // Save the complete report
        localStorage.setItem("scabbiaResult", JSON.stringify(report));
        setReport(report);
      } catch (err) {
        console.error("Error:", err);
        setError(err instanceof Error ? err.message : "Si è verificato un errore");
      } finally {
        setLoading(false);
      }
    };

    if (sessionId) {
      fetchAnalysis();
    } else {
      // If no session ID, try to load existing result
      const stored = localStorage.getItem("scabbiaResult");
      if (stored) {
        try {
          setReport(JSON.parse(stored));
        } catch {
          setError("Errore nel caricamento del risultato");
        }
      } else {
        setError("Nessun risultato trovato. Per favore, completa il questionario.");
      }
      setLoading(false);
    }
  }, [sessionId]);

  // Loading Screen
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center space-y-6"
        >
          <motion.div
            animate={{
              scale: [1, 1.1, 1],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="w-16 h-16 mx-auto bg-gradient-to-r from-primary to-primary-dark rounded-full flex items-center justify-center"
          >
            <div className="w-8 h-8 bg-white rounded-full" />
          </motion.div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-foreground">Elaborazione del risultato...</h2>
            <p className="text-muted-foreground">Stiamo preparando la tua analisi personalizzata</p>
          </div>
        </motion.div>
      </div>
    );
  }

  // Error Screen
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-destructive/5 via-background to-destructive/10 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="max-w-md mx-auto text-center space-y-6 p-8 bg-card rounded-xl shadow-lg"
        >
          <div className="text-destructive">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="space-y-2">
            <h2 className="text-xl font-bold text-destructive">Errore</h2>
            <p className="text-muted-foreground">{error}</p>
          </div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/scabbia-checker"
              className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium"
            >
              Torna al questionario
            </Link>
          </motion.div>
        </motion.div>
      </div>
    );
  }

  // Main Results Screen
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b p-4"
      >
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/scabbia-checker"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Nuovo Test
            </Link>
          </motion.div>

          <div className="flex items-center gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <Share2 className="w-4 h-4" />
              Condividi
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <Download className="w-4 h-4" />
              Scarica PDF
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Results Content */}
      <div className="max-w-6xl mx-auto p-4 pt-8">
        {report ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <ResultVisualization
              probability={report.probability}
              summary={report.summary}
              details={report.details}
              analysis={report.analysis}
            />
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-20"
          >
            <div className="space-y-4">
              <div className="text-muted-foreground">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-foreground">Nessun risultato disponibile</h2>
              <p className="text-muted-foreground">Completa il questionario per vedere i risultati</p>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  href="/scabbia-checker"
                  className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium mt-4"
                >
                  Inizia il Test
                </Link>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Footer Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="max-w-6xl mx-auto p-4 pb-8"
      >
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/scabbia-checker"
              className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium text-center"
            >
              Nuova Analisi
            </Link>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/"
              className="inline-block bg-muted text-foreground px-6 py-3 rounded-lg font-medium text-center hover:bg-muted/80"
            >
              Torna alla Home
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
