"use client"

import { motion } from "framer-motion"
import { PieChart, Pie, Cell, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from "recharts"
import { AlertTriangle, CheckCircle, Activity } from "lucide-react"
import { AnimatedCard } from "./animated-card"

interface ResultVisualizationProps {
  probability: number
  summary: string
  details: string
  analysis?: string
}

export function ResultVisualization({ probability, summary, details, analysis }: ResultVisualizationProps) {
  const getRiskLevel = (prob: number) => {
    if (prob >= 75) return { level: "Alto", color: "red", icon: AlertTriangle }
    if (prob >= 50) return { level: "Moderato", color: "yellow", icon: Activity }
    if (prob >= 25) return { level: "Basso", color: "blue", icon: CheckCircle }
    return { level: "Molto Basso", color: "green", icon: CheckCircle }
  }

  const risk = getRiskLevel(probability)
  const RiskIcon = risk.icon

  // Dati per il grafico a torta
  const pieData = [
    { name: "Probabilità", value: probability, color: `hsl(var(--${risk.color === "red" ? "destructive" : risk.color === "yellow" ? "warning" : risk.color === "blue" ? "primary" : "success"}))` },
    { name: "Restante", value: 100 - probability, color: "hsl(var(--muted))" }
  ]

  // Dati simulati per il radar chart (basati sui sintomi)
  const radarData = [
    { symptom: "Prurito", value: probability > 50 ? 85 : 30 },
    { symptom: "Eruzioni", value: probability > 50 ? 70 : 25 },
    { symptom: "Cunicoli", value: probability > 50 ? 60 : 15 },
    { symptom: "Contatti", value: probability > 50 ? 75 : 20 },
    { symptom: "Localizzazione", value: probability > 50 ? 80 : 35 },
  ]

  return (
    <div className="space-y-8">
      {/* Header con animazione */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center space-y-4"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
        >
          <RiskIcon className={`w-16 h-16 mx-auto ${
            risk.color === "red" ? "text-destructive" : 
            risk.color === "yellow" ? "text-warning" : 
            risk.color === "blue" ? "text-primary" : 
            "text-success"
          }`} />
        </motion.div>
        <h1 className="text-3xl font-bold text-foreground">Risultato Analisi</h1>
        <p className="text-muted-foreground text-lg">Ecco la valutazione dei tuoi sintomi</p>
      </motion.div>

      {/* Gauge principale */}
      <AnimatedCard delay={0.2} className="p-8">
        <div className="text-center space-y-6">
          <h2 className="text-2xl font-semibold text-foreground">Probabilità di Scabbia</h2>
          
          {/* Gauge Chart */}
          <div className="relative h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  startAngle={180}
                  endAngle={0}
                  innerRadius={60}
                  outerRadius={100}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
            
            {/* Percentage in center */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.8, type: "spring" }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <div className="text-center">
                <div className={`text-4xl font-bold ${
                  risk.color === "red" ? "text-destructive" : 
                  risk.color === "yellow" ? "text-warning" : 
                  risk.color === "blue" ? "text-primary" : 
                  "text-success"
                }`}>
                  {probability}%
                </div>
                <div className="text-sm text-muted-foreground">Rischio {risk.level}</div>
              </div>
            </motion.div>
          </div>
        </div>
      </AnimatedCard>

      {/* Analisi dei sintomi */}
      <AnimatedCard delay={0.4} className="p-8">
        <h3 className="text-xl font-semibold mb-6 text-center">Analisi dei Sintomi</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={radarData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="symptom" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar
                name="Intensità"
                dataKey="value"
                stroke="hsl(var(--primary))"
                fill="hsl(var(--primary))"
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </RadarChart>
          </ResponsiveContainer>
        </div>
      </AnimatedCard>

      {/* Riepilogo e dettagli */}
      <div className="grid md:grid-cols-2 gap-6">
        <AnimatedCard delay={0.6} className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-primary" />
            Riepilogo
          </h3>
          <p className="text-muted-foreground leading-relaxed">{summary}</p>
        </AnimatedCard>

        <AnimatedCard delay={0.8} className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Activity className="w-5 h-5 text-primary" />
            Dettagli
          </h3>
          <p className="text-muted-foreground leading-relaxed">{details}</p>
        </AnimatedCard>
      </div>

      {/* Analisi approfondita se disponibile */}
      {analysis && (
        <AnimatedCard delay={1.0} className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-warning" />
            Analisi Approfondita
          </h3>
          <div className="prose prose-sm max-w-none text-muted-foreground">
            <pre className="whitespace-pre-wrap font-sans">{analysis}</pre>
          </div>
        </AnimatedCard>
      )}

      {/* Disclaimer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
        className="text-center p-6 bg-muted/50 rounded-lg"
      >
        <p className="text-sm text-muted-foreground">
          ⚠️ Questa analisi non sostituisce il parere di un medico. 
          Si consiglia di consultare un professionista sanitario per una diagnosi accurata.
        </p>
      </motion.div>
    </div>
  )
}
